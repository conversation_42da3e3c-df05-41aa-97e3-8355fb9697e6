/* ==================== 基础工具样式 ==================== */
.xiaobaix-iframe {
    transition: height 0.3s ease;
}

pre:has(+ .xiaobaix-iframe) {
    display: none;
}

.xiaobaix-iframe-wrapper {
    margin: 10px 0;
}

/* ==================== 动画效果 ==================== */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInTop {
    from { transform: translateY(-100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes popIn {
    from { transform: scale(0.95); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* ==================== 记忆按钮样式 ==================== */
.mes_btn.memory-button {
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.mes_btn.memory-button:hover {
    opacity: 1;
}

.mes_btn.memory-button.has-memory {
    color: var(--SmartThemeAccent);
}

/* ==================== 模态框基础样式 ==================== */
.memory-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--black30a);
    backdrop-filter: blur(calc(var(--SmartThemeBlurStrength) * 2));
    -webkit-backdrop-filter: blur(calc(var(--SmartThemeBlurStrength) * 2));
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.2s ease;
}

.memory-modal-content {
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 10px;
    width: 85%;
    max-width: 700px;
    max-height: 85vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0px 0px 14px var(--black70a);
    animation: slideIn 0.3s ease;
    color: var(--SmartThemeBodyColor);
}

.memory-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--SmartThemeBorderColor);
    background: var(--SmartThemeBlurTintColor);
    border-radius: 10px 10px 0 0;
}

.memory-modal-title {
    font-size: var(--mainFontSize);
    font-weight: 600;
    color: var(--SmartThemeBodyColor);
}

.memory-modal-close {
    font-size: 1.2em;
    cursor: pointer;
    opacity: 0.7;
    transition: all 0.2s ease;
    color: var(--SmartThemeBodyColor);
    padding: 4px 8px;
    border-radius: 4px;
}

.memory-modal-close:hover {
    opacity: 1;
    background: var(--white30a);
}

.memory-tab-content {
    padding: 8px 12px;
    overflow-y: auto;
    flex-grow: 1;
    font-size: calc(var(--mainFontSize) * 0.95);
    line-height: 1.4;
}

#memory-stats-content {
    padding: 8px 12px;
    overflow-y: auto;
    flex-grow: 1;
    font-size: calc(var(--mainFontSize) * 0.95);
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.memory-modal-footer {
    padding: 12px 16px;
    border-top: 1px solid var(--SmartThemeBorderColor);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    background: var(--SmartThemeBlurTintColor);
    border-radius: 0 0 10px 10px;
    flex-wrap: wrap;
}

.behavior-footer-left,
.behavior-footer-right,
.main-menu-footer-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

.memory-action-button {
    color: var(--SmartThemeBodyColor);
    background-color: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 6px;
    padding: 6px 12px;
    cursor: pointer;
    font-size: calc(var(--mainFontSize) * 0.9);
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    white-space: nowrap;
    min-height: 28px;
    filter: grayscale(0.3);
}

.memory-action-button:hover {
    background-color: var(--white30a);
    filter: grayscale(0);
    transform: translateY(-1px);
}

.memory-action-button.secondary {
    filter: grayscale(0.6);
}

.memory-action-button.primary {
    background-color: var(--crimson70a);
    color: var(--white);
    filter: grayscale(0);
}

.memory-action-button.primary:hover {
    background-color: var(--crimson);
}

/* ==================== 行为设置模态框 ==================== */
.behavior-modal-content {
    width: 90%;
    max-width: 800px;
    max-height: 85vh;
}

.user-settings-section {
    margin-bottom: 20px;
    padding: 15px;
    background: var(--black10a);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 8px;
}

.user-gender-setting {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.user-gender-setting label {
    font-weight: 500;
    margin-right: 10px;
}

.user-gender-select {
    width: 120px;
    padding: 6px 8px;
    background: var(--SmartThemeBlurTintColor);
    color: var(--SmartThemeBodyColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 6px;
    transition: all 0.2s ease;
}

.user-gender-select:focus {
    outline: none;
    border-color: var(--SmartThemeAccent);
    box-shadow: 0 0 0 2px var(--SmartThemeAccent30a);
}

.user-aliases-setting {
    margin-top: 15px;
}

.user-aliases-setting label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.user-aliases-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
    min-height: 32px;
    padding: 12px;
    background: var(--black10a);
    border-radius: 6px;
    border: 1px dashed var(--SmartThemeBorderColor);
}

.user-alias-item {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 4px 10px;
    background: var(--SmartThemeAccent);
    color: var(--SmartThemeAccentText);
    border-radius: 16px;
    font-size: calc(var(--mainFontSize) * 0.85);
    font-weight: 500;
    transition: all 0.2s ease;
}

.user-alias-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px var(--black30a);
}

.alias-text {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.remove-alias {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
    opacity: 0.7;
}

.remove-alias:hover {
    background: rgba(255, 255, 255, 0.25);
    opacity: 1;
}

.add-alias-container {
    display: flex;
    gap: 8px;
    align-items: center;
}

.alias-input {
    flex: 1;
    min-width: 140px;
    background: var(--SmartThemeBlurTintColor);
    color: var(--SmartThemeBodyColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: calc(var(--mainFontSize) * 0.9);
    transition: all 0.2s ease;
}

.alias-input:focus {
    outline: none;
    border-color: var(--SmartThemeAccent);
    box-shadow: 0 0 0 2px var(--SmartThemeAccent30a);
}

.add-alias-button {
    background: var(--SmartThemeAccent);
    color: var(--SmartThemeAccentText);
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: calc(var(--mainFontSize) * 0.9);
    font-weight: 500;
    white-space: nowrap;
}

.add-alias-button:hover {
    background: var(--SmartThemeAccentHover);
    transform: translateY(-1px);
}

.setting-desc {
    font-size: 0.85em;
    color: var(--SmartThemeBodyColor);
    opacity: 0.7;
    margin-top: 5px;
    margin-bottom: 0;
}

.tracked-names-section {
    margin-bottom: 20px;
}

.section-desc {
    font-size: 0.9em;
    color: var(--SmartThemeBodyColor);
    opacity: 0.8;
    margin-bottom: 10px;
}

.tracked-names-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 12px;
    min-height: 32px;
    padding: 12px;
    background: var(--black10a);
    border-radius: 6px;
    border: 1px dashed var(--SmartThemeBorderColor);
}

.tracked-name-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: var(--SmartThemeAccent);
    color: var(--SmartThemeAccentText);
    border-radius: 16px;
    font-size: calc(var(--mainFontSize) * 0.85);
    font-weight: 500;
    transition: all 0.2s ease;
}

.tracked-name-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px var(--black30a);
}

.tracked-name {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.tracked-gender-select {
    min-width: 100px;
    padding: 6px 8px;
    background: var(--SmartThemeBlurTintColor);
    color: var(--SmartThemeBodyColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 6px;
    transition: all 0.2s ease;
    margin: 0 5px;
}

.tracked-gender-select:focus {
    outline: none;
    border-color: var(--SmartThemeAccent);
    box-shadow: 0 0 0 2px var(--SmartThemeAccent30a);
}

.gender-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9em;
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    margin-left: 5px;
}

.tracked-name-stats {
    display: flex;
    gap: 5px;
    margin-left: 4px;
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    padding-left: 8px;
}

.intimacy-value,
.initial-intimacy-value,
.interactions-value {
    font-size: 0.85em;
    padding: 2px 4px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.15);
    white-space: nowrap;
}

.tracked-name-actions {
    display: flex;
    gap: 3px;
    margin-left: 3px;
}

.edit-name, .remove-name {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
    opacity: 0.7;
}

.edit-name:hover, .remove-name:hover {
    background: rgba(255, 255, 255, 0.25);
    opacity: 1;
}

.tracked-intimacy-input {
    width: 100px;
    background: var(--SmartThemeBlurTintColor);
    color: var(--SmartThemeBodyColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: calc(var(--mainFontSize) * 0.9);
    transition: all 0.2s ease;
    text-align: center;
}

.tracked-intimacy-input:focus {
    outline: none;
    border-color: var(--SmartThemeAccent);
    box-shadow: 0 0 0 2px var(--SmartThemeAccent30a);
}

.add-name-container {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.tracked-name-input {
    flex: 1;
    min-width: 140px;
    background: var(--SmartThemeBlurTintColor);
    color: var(--SmartThemeBodyColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: calc(var(--mainFontSize) * 0.9);
    transition: all 0.2s ease;
}

.tracked-name-input:focus {
    outline: none;
    border-color: var(--SmartThemeAccent);
    box-shadow: 0 0 0 2px var(--SmartThemeAccent30a);
}

.add-name-button {
    background: var(--SmartThemeAccent);
    color: var(--SmartThemeAccentText);
    border: 1px solid var(--SmartThemeAccent);
    border-radius: 6px;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: calc(var(--mainFontSize) * 0.9);
    font-weight: 500;
    white-space: nowrap;
}

.add-name-button:hover {
    background: var(--SmartThemeAccentHover);
    transform: translateY(-1px);
}

.behavior-stages-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 6px;
    padding: 4px;
}

.behavior-stage-tab {
    padding: 8px 12px;
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: calc(var(--mainFontSize) * 0.85);
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--SmartThemeBodyColor);
}

.behavior-stage-tab:hover {
    background: var(--white20a);
    transform: translateY(-1px);
}

.behavior-stage-tab.active {
    background: var(--SmartThemeAccent);
    color: var(--SmartThemeAccentText);
    border-color: var(--SmartThemeAccent);
    box-shadow: 0 2px 8px var(--SmartThemeAccent30a);
}

.behavior-stage-content {
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 8px;
    padding: 0;
    overflow: hidden;
}

.behavior-stage-form {
    padding: 20px;
}

.behavior-stage-form h3 {
    margin: 0 0 16px 0;
    color: var(--SmartThemeBodyColor);
    font-size: calc(var(--mainFontSize) * 1.1);
    font-weight: 600;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--SmartThemeAccent);
}

.stage-range {
    font-size: 0.8em;
    font-weight: normal;
    opacity: 0.7;
}

.behavior-field {
    margin-bottom: 16px;
}

.behavior-field label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    font-size: calc(var(--mainFontSize) * 0.95);
    color: var(--SmartThemeBodyColor);
}

.behavior-textarea {
    width: 100%;
    min-height: 60px;
    background: var(--SmartThemeBlurTintColor);
    color: var(--SmartThemeBodyColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 6px;
    padding: 12px;
    resize: vertical;
    font-family: inherit;
    font-size: calc(var(--mainFontSize) * 0.9);
    line-height: 1.4;
    transition: all 0.2s ease;
}

.behavior-textarea:focus {
    outline: none;
    border-color: var(--SmartThemeAccent);
    box-shadow: 0 0 0 2px var(--SmartThemeAccent30a);
}

/* ==================== 编辑人物对话框 ==================== */
.xiaobaix-edit-name-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh !important;
    background: var(--black50a);
    backdrop-filter: blur(calc(var(--SmartThemeBlurStrength) * 3));
    -webkit-backdrop-filter: blur(calc(var(--SmartThemeBlurStrength) * 3));
    z-index: 10002;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.2s ease;
}

.xiaobaix-edit-name-content {
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 12px;
    width: 90%;
    max-width: 350px;
    padding: 20px;
    box-shadow: 0px 8px 32px var(--black70a);
    animation: popIn 0.3s ease;
    color: var(--SmartThemeBodyColor);
}

.xiaobaix-edit-name-content h3 {
    margin: 0 0 16px 0;
    text-align: center;
    font-weight: 600;
}

.edit-name-field {
    margin-bottom: 16px;
}

.edit-name-field label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    font-size: calc(var(--mainFontSize) * 0.9);
}

.edit-name-field input {
    width: 100%;
    background: var(--SmartThemeBlurTintColor);
    color: var(--SmartThemeBodyColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: calc(var(--mainFontSize) * 0.9);
    transition: all 0.2s ease;
}

.edit-name-field input:focus {
    outline: none;
    border-color: var(--SmartThemeAccent);
    box-shadow: 0 0 0 2px var(--SmartThemeAccent30a);
}

.edit-name-field input[readonly] {
    background: var(--black30a);
    cursor: not-allowed;
}

.xiaobaix-edit-name-buttons {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    margin-top: 20px;
}

.xiaobaix-edit-name-save,
.xiaobaix-edit-name-cancel {
    flex: 1;
    padding: 8px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
}

.xiaobaix-edit-name-save {
    background: var(--SmartThemeAccent);
    color: var(--SmartThemeAccentText);
}

.xiaobaix-edit-name-cancel {
    background: var(--SmartThemeBorder);
    color: var(--SmartThemeBodyColor);
}

.xiaobaix-edit-name-save:hover,
.xiaobaix-edit-name-cancel:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px var(--black30a);
}

/* ==================== 确认对话框 ==================== */
.xiaobaix-confirm-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100vh !important;
    background: var(--black50a) !important;
    backdrop-filter: blur(calc(var(--SmartThemeBlurStrength) * 3)) !important;
    -webkit-backdrop-filter: blur(calc(var(--SmartThemeBlurStrength) * 3)) !important;
    z-index: 99999 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    animation: fadeIn 0.2s ease !important;
}

.xiaobaix-confirm-content {
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 12px;
    width: 90%;
    max-width: 450px;
    padding: 20px;
    box-shadow: 0px 8px 32px var(--black70a);
    animation: slideIn 0.3s ease;
    text-align: center;
    color: var(--SmartThemeBodyColor);
}

.xiaobaix-confirm-message {
    margin-bottom: 24px;
    font-size: calc(var(--mainFontSize) * 1.05);
    line-height: 1.5;
}

.xiaobaix-confirm-buttons {
    display: flex;
    justify-content: center;
    gap: 12px;
}

.xiaobaix-confirm-yes,
.xiaobaix-confirm-no {
    padding: 10px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: calc(var(--mainFontSize) * 0.95);
    font-weight: 500;
    transition: all 0.2s ease;
    min-width: 80px;
}

.xiaobaix-confirm-yes {
    background: var(--crimson);
    color: var(--white);
}

.xiaobaix-confirm-no {
    background: var(--SmartThemeBorder);
    color: var(--SmartThemeBodyColor);
}

.xiaobaix-confirm-yes:hover,
.xiaobaix-confirm-no:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--black30a);
}

/* ==================== 统计编辑器样式 ==================== */
.stats-editor {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.stats-section {
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    padding: 16px;
    border-radius: 8px;
}

.stats-section h3 {
    margin: 0 0 12px 0;
    color: var(--SmartThemeBodyColor);
    font-weight: 600;
    border-bottom: 1px solid var(--SmartThemeBorderColor);
    padding-bottom: 6px;
}

.stats-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.stats-field label {
    flex: 1;
    font-size: calc(var(--mainFontSize) * 0.9);
}

.stats-field input {
    width: 80px;
    background: var(--SmartThemeInputColor);
    color: var(--SmartThemeText);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 4px;
    padding: 6px;
    text-align: center;
}

.stats-empty-message {
    font-style: italic;
    opacity: 0.7;
    text-align: center;
    padding: 10px;
}

.stats-field .relationship-stage {
    margin-left: 10px;
    font-size: calc(var(--mainFontSize) * 0.85);
    color: var(--SmartThemeBodyColor);
    opacity: 0.7;
    white-space: nowrap;
}

/* ==================== 调试日志模态框 ==================== */
.debug-log-modal {
    position: fixed !important;
    inset: 0 !important;
    background: var(--black50a) !important;
    backdrop-filter: blur(calc(var(--SmartThemeBlurStrength) * 2)) !important;
    z-index: 10000 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    animation: fadeIn 0.3s ease !important;
}

.debug-log-content {
    background: var(--SmartThemeBlurTintColor) !important;
    border: 1px solid var(--SmartThemeBorderColor) !important;
    border-radius: 10px !important;
    display: flex !important;
    flex-direction: column !important;
    box-shadow: 0 8px 32px var(--black70a) !important;
    color: var(--SmartThemeBodyColor) !important;
    overflow: hidden !important;
    animation: slideIn 0.3s ease !important;
}

.debug-log-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 12px 16px !important;
    border-bottom: 1px solid var(--SmartThemeBorderColor) !important;
    flex-shrink: 0 !important;
}

.debug-log-title {
    font-size: var(--mainFontSize) !important;
    font-weight: 600 !important;
    margin: 0 !important;
}

.debug-log-close {
    font-size: 1.2em !important;
    cursor: pointer !important;
    opacity: 0.7 !important;
    transition: all 0.2s ease !important;
    padding: 4px 8px !important;
    border-radius: 4px !important;
}

.debug-log-close:hover {
    opacity: 1 !important;
    background: var(--white30a) !important;
}

.debug-log-text {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 16px !important;
    font-family: 'Courier New', monospace !important;
    font-size: calc(var(--mainFontSize) * 0.85) !important;
    line-height: 1.5 !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
}

.debug-log-text.loading::before {
    content: "🔄 正在分析计算过程...";
    display: block;
    text-align: center;
    color: var(--SmartThemeAccent);
    font-style: italic;
    margin-bottom: 10px;
}

.debug-log-footer {
    padding: 12px 16px !important;
    border-top: 1px solid var(--SmartThemeBorderColor) !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 10px !important;
    flex-shrink: 0 !important;
}

.debug-log-button {
    background: var(--SmartThemeBlurTintColor) !important;
    color: var(--SmartThemeBodyColor) !important;
    border: 1px solid var(--SmartThemeBorderColor) !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    cursor: pointer !important;
    font-size: calc(var(--mainFontSize) * 0.9) !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    min-width: 80px !important;
}

.debug-log-button:hover {
    background: var(--white30a) !important;
}

#copy-debug-log {
    background: var(--SmartThemeAccent) !important;
    color: var(--SmartThemeAccentText) !important;
    border-color: var(--SmartThemeAccent) !important;
}

#copy-debug-log:hover {
    background: var(--SmartThemeAccentHover) !important;
}

/* ==================== 循环任务样式 ==================== */
.task-container {
    margin-top: 10px;
    margin-bottom: 10px;
}

.task-container:empty::after {
    content: "No tasks found";
    font-size: 0.95em;
    opacity: 0.7;
    display: block;
    text-align: center;
}

.scheduled-tasks-embedded-warning {
    padding: 15px;
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 8px;
    margin: 10px 0;
}

.warning-note {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 10px;
    padding: 8px;
    background: rgba(255, 193, 7, 0.1);
    border-left: 3px solid #ffc107;
    border-radius: 4px;
}

.task-item {
    align-items: center;
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 10px;
    padding: 0 5px;
    margin-top: 1px;
    margin-bottom: 1px;
}

.task-item:has(.disable_task:checked) .task_name {
    text-decoration: line-through;
    filter: grayscale(0.5);
}

.task_name {
    font-weight: normal;
    color: var(--SmartThemeEmColor);
    font-size: 0.9em;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.drag-handle {
    cursor: grab;
    color: var(--SmartThemeQuoteColor);
    margin-right: 8px;
    user-select: none;
}

.drag-handle:active {
    cursor: grabbing;
}

.menu_button {
    width: fit-content;
    display: flex;
    gap: 10px;
    flex-direction: row;
}

.checkbox {
    align-items: center;
}

.task_editor {
    width: 100%;
}

.task_editor .flex-container {
    gap: 10px;
}

.task_editor textarea {
    font-family: 'Courier New', monospace;
}

input.disable_task {
    display: none !important;
}

.task-toggle-off {
    cursor: pointer;
    opacity: 0.5;
    filter: grayscale(0.5);
    transition: opacity 0.2s ease-in-out;
}

.task-toggle-off:hover {
    opacity: 1;
    filter: none;
}

.task-toggle-on {
    cursor: pointer;
}

.disable_task:checked ~ .task-toggle-off {
    display: block;
}

.disable_task:checked ~ .task-toggle-on {
    display: none;
}

.disable_task:not(:checked) ~ .task-toggle-off {
    display: none;
}

.disable_task:not(:checked) ~ .task-toggle-on {
    display: block;
}

/* ==================== 沉浸式显示模式样式 ==================== */
body.immersive-mode #chat {
   padding: 0 !important;
   border: 0px !important;
   overflow-y: auto;
   margin: 0 !important;
}

body.immersive-mode .mesAvatarWrapper,
body.immersive-mode .timestamp,
body.immersive-mode .swipe_left,
body.immersive-mode .swipeRightBlock {
   display: none !important;
}

body.immersive-mode .mes {
   margin: 2% 0 0 0 !important;
}

body.immersive-mode .mes_block {
   padding-left: 0 !important;
   margin: 0 !important;
}

body.immersive-mode .mes_text {
   padding: 0px !important;
   max-width: 100%;
   width: 100%;
}

body.immersive-mode .mes {
   width: 99%;
   margin: 0 0.5%;
   padding: 0px !important;
}

.immersive-navigation {
    position: absolute;
    transform: translateX(-50%);
    left: 50%;
    bottom: 5%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(51, 51, 51, 0.3);
    border-radius: 25px;
    width: fit-content;
    max-width: 50%;
    z-index: -1;
    height: 25px;
    box-sizing: border-box;
}

.immersive-nav-btn {
   background: none;
   border: none;
   color: var(--SmartThemeBodyColor);
   cursor: pointer;
   border-radius: 50%;
   transition: all 0.2s ease;
   font-size: 12px;
   display: flex;
   align-items: center;
   justify-content: center;
   min-width: 24px;
   height: 24px;
}

.immersive-nav-btn:hover:not(:disabled) {
   background-color: rgba(var(--SmartThemeBodyColor), 0.2);
   transform: scale(1.1);
}

.immersive-nav-btn:disabled {
   opacity: 0.3;
   cursor: not-allowed;
}

/* ==================== 模板编辑器样式 ==================== */
.xiaobai_template_editor {
    max-height: 80vh;
    overflow-y: auto;
    padding: 20px;
    border-radius: 8px;
}

.template-replacer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.template-replacer-title {
    font-weight: bold;
    color: var(--SmartThemeEmColor, #007bff);
}

.template-replacer-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.template-replacer-status {
    font-size: 12px;
    color: var(--SmartThemeQuoteColor, #888);
    font-style: italic;
}

.template-replacer-status.has-settings {
    color: var(--SmartThemeEmColor, #007bff);
}

.template-replacer-status.no-character {
    color: var(--SmartThemeCheckboxBgColor, #666);
}

.example-text {
    font-size: 0.9em;
    color: var(--SmartThemeQuoteColor, #888);
}

.example-text p {
    margin: 5px 0;
    text-align: left !important;
}

.example-text strong {
    color: var(--SmartThemeEmColor);
}

/* ==================== 消息预览插件样式 ==================== */
#message_preview_btn {
    width: var(--bottomFormBlockSize);
    height: var(--bottomFormBlockSize);
    margin: 0;
    border: none;
    cursor: pointer;
    opacity: 0.7;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 300ms;
    color: var(--SmartThemeBodyColor);
    font-size: var(--bottomFormIconSize);
}

#message_preview_btn:hover {
    opacity: 1;
    filter: brightness(1.2);
}

.message-preview-popup {
    max-height: 82vh;
    overflow-y: auto;
    padding: 10px;
}

.message-preview-content-box {
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    white-space: pre-wrap;
    max-height: 82vh;
    overflow-y: auto;
    padding: 15px;
    background: #000000 !important;
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 5px;
    color: #ffffff !important;
    font-size: 12px;
    line-height: 1.4;
    text-align: left;
}

.mes_history_preview {
    opacity: 0.6;
    transition: opacity 0.2s ease-in-out;
}

.mes_history_preview:hover {
    opacity: 1;
}

/* ==================== auto-backgroung样式 ==================== */
.settings-menu {
    display: flex;
    margin-bottom: 10px;
    border-bottom: 1px solid #444;
}
.menu-tab {
    flex: 1;
    padding: 2px 8px;
    text-align: center;
    cursor: pointer;
    color: #ccc;
    border: none;
    transition: color 0.2s ease;
    font-weight: 500;
}
.menu-tab:hover {
    color: #fff;
}
.menu-tab.active {
    color: #007acc;
    border-bottom: 2px solid #007acc;
}
.settings-section {
    padding: 10px 0;
}
.template-replacer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}
.template-replacer-title {
    font-weight: bold;
    color: #007acc;
}
.template-replacer-status {
    padding: 5px 10px;
    background: #2a2a2a;
    border-radius: 4px;
    margin-bottom: 10px;
    font-size: 0.9em;
}
.template-replacer-status.has-settings {
    background: #1a4a1a;
    color: #90ee90;
}
.template-replacer-status.no-character {
    background: #4a1a1a;
    color: #ffb3b3;
}
.custom-tags-container {
    margin-top: 10px;
}
.custom-tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
    min-height: 20px;
    padding: 8px;
    background: #2a2a2a;
    border-radius: 4px;
    border: 1px solid #444;
}
.custom-tag-item {
    display: flex;
    align-items: center;
    background: #007acc;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    gap: 6px;
}
.custom-tag-text {
    font-weight: 500;
}
.custom-tag-remove {
    cursor: pointer;
    color: rgba(255, 255, 255, 0.8);
    font-weight: bold;
    transition: color 0.2s ease;
}
.custom-tag-remove:hover {
    color: #ff6b6b;
}
.custom-tags-empty {
    color: #888;
    font-style: italic;
    font-size: 12px;
    text-align: center;
    padding: 8px;
}

/* ==================== 角色卡自动更新样式 (Character Updater) ==================== */
/* 当前角色显示 - 在 settings.html 中 */
.littlewhitebox .current-character-info {
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 5px;
    padding: 10px;
    margin: 15px 0 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.littlewhitebox .current-character-info:hover {
    background: var(--SmartThemeBorderColor);
    border-color: var(--SmartThemeBodyColor);
}

.littlewhitebox .current-character-name {
    font-weight: 500;
    color: var(--SmartThemeBodyColor);
    font-size: var(--mainFontSize);
}

.littlewhitebox .character-status {
    font-size: 11px;
    padding: 3px 8px;
    border-radius: 10px;
    font-weight: 500;
}

.littlewhitebox .character-status.bound {
    background: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
    border: 1px solid #2ecc71;
}

.littlewhitebox .character-status.unbound {
    background: rgba(241, 196, 15, 0.2);
    color: #f1c40f;
    border: 1px solid #f1c40f;
}

/* 角色列表更新通知样式 - 在角色列表页面 */
.character-update-notification {
    display: inline-flex;
    align-items: center;
    gap: 3px;
    background: rgba(46, 204, 113, 0.9);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 5px;
    white-space: nowrap;
}

.character-update-notification:hover {
    background: rgba(46, 204, 113, 1);
    transform: scale(1.05);
}

.character-update-notification i {
    font-size: 9px;
    color: white;
}

.character-update-notification small {
    font-size: 9px;
    font-weight: 500;
    color: white;
    margin: 0;
    opacity: 1;
}

/* 角色编辑表单按钮样式 - 在角色编辑页面 */
#character-updater-edit-button {
    color: var(--SmartThemeBodyColor);
    filter: grayscale(0.5);
    background-color: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 5px;
    cursor: pointer;
    transition: var(--animation-duration-2x);
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px;
    margin: 0;
}

#character-updater-edit-button:hover:not(:disabled) {
    filter: grayscale(0);
    background-color: var(--SmartThemeBorderColor);
}

#character-updater-edit-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#character-updater-edit-button.has-update {
    color: #2ecc71;
    filter: grayscale(0);
}

#character-updater-edit-button.has-update:hover:not(:disabled) {
    background-color: rgba(46, 204, 113, 0.2);
    border-color: #2ecc71;
}

/* ==================== 菜单覆盖层样式 - 全局 ==================== */
.character-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1rem;
}

.character-menu-content {
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 10px;
    width: 100%;
    max-width: 500px;
    max-height: 85vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(var(--SmartThemeBlurStrength));
}

.menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--SmartThemeBorderColor);
    background: var(--SmartThemeBlurTintColor);
    border-radius: 10px 10px 0 0;
}

.menu-header h3 {
    margin: 0;
    color: var(--SmartThemeBodyColor);
    font-size: 18px;
    font-weight: 600;
}

.menu-close {
    background: none;
    border: none;
    color: var(--SmartThemeBodyColor);
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.menu-close:hover {
    background: var(--SmartThemeBorderColor);
}

.menu-body {
    padding: 20px;
}

.menu-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--SmartThemeBorderColor);
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    background: var(--SmartThemeBlurTintColor);
    border-radius: 0 0 10px 10px;
}

/* ==================== UUID显示样式 ==================== */
.uuid-display {
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 15px;
}

.uuid-display label {
    display: block;
    color: var(--SmartThemeBodyColor);
    font-weight: 500;
    font-size: 13px;
    margin-bottom: 5px;
}

.uuid-value {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: var(--SmartThemeBodyColor);
    background: rgba(0, 0, 0, 0.1);
    padding: 5px 8px;
    border-radius: 3px;
    word-break: break-all;
    display: block;
}

/* ==================== 表单组样式 ==================== */
.menu-body .form-group {
    margin-bottom: 15px;
}

.menu-body .form-group label {
    display: block;
    margin-bottom: 5px;
    color: var(--SmartThemeBodyColor);
    font-weight: 500;
    font-size: 13px;
    text-align: left;
}

.menu-body input[type="text"],
.menu-body input[type="password"],
.menu-body input[type="url"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 5px;
    background: var(--SmartThemeBlurTintColor);
    color: var(--SmartThemeBodyColor);
    font-size: 13px;
    transition: border-color 0.2s ease;
    box-sizing: border-box;
}

.menu-body input[type="text"]:focus,
.menu-body input[type="password"]:focus,
.menu-body input[type="url"]:focus {
    outline: none;
    border-color: var(--SmartThemeBodyColor);
}

/* ==================== 菜单按钮样式 ==================== */
.menu-button {
    padding: 8px 16px;
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 5px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
    min-width: 80px;
}

.confirm-button {
    background: #27ae60;
    color: white;
    border-color: #27ae60;
}

.confirm-button:hover:not(:disabled) {
    background: #219a52;
    border-color: #219a52;
}

.confirm-button:disabled {
    background: #95a5a6;
    border-color: #95a5a6;
    cursor: not-allowed;
    opacity: 0.6;
}

.silent-update-button {
    background: #f39c12;
    color: white;
    border-color: #f39c12;
}

.silent-update-button:hover:not(:disabled) {
    background: #e67e22;
    border-color: #e67e22;
}

.silent-update-button:disabled {
    background: #95a5a6;
    border-color: #95a5a6;
    cursor: not-allowed;
    opacity: 0.6;
}

.cancel-button {
    background: var(--SmartThemeBlurTintColor);
    color: var(--SmartThemeBodyColor);
}

.cancel-button:hover {
    background: var(--SmartThemeBorderColor);
}

/* ==================== 更新弹窗样式 ==================== */
.character-update-popup {
    padding: 20px;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    color: var(--SmartThemeBodyColor);
}

.character-update-popup h3 {
    margin: 0 0 15px 0;
    color: var(--SmartThemeBodyColor);
    font-size: 18px;
    font-weight: 600;
    border-bottom: 1px solid var(--SmartThemeBorderColor);
    padding-bottom: 10px;
}

.character-update-popup .update-status {
    margin-bottom: 15px;
}

.character-update-popup .update-status .status-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    border-radius: 5px;
    font-weight: 500;
}

.character-update-popup .update-status .status-success {
    background: rgba(46, 204, 113, 0.1);
    color: #2ecc71;
    border: 1px solid #2ecc71;
}

.character-update-popup .update-status .status-success i {
    color: #2ecc71;
}

.character-update-popup .update-status .status-update {
    background: rgba(243, 156, 18, 0.1);
    color: #f39c12;
    border: 1px solid #f39c12;
}

.character-update-popup .update-status .status-update i {
    color: #f39c12;
}

.character-update-popup .update-description {
    margin-bottom: 15px;
}

.character-update-popup .update-description strong {
    display: block;
    margin-bottom: 5px;
    color: var(--SmartThemeBodyColor);
    font-weight: 600;
}

.character-update-popup .update-description p {
    margin: 0;
    padding: 8px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    font-style: italic;
    color: var(--SmartThemeBodyColor);
    opacity: 0.9;
    max-height: 120px;
    overflow-y: auto;
    word-wrap: break-word;
}

.character-update-popup .update-timestamps {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.character-update-popup .update-timestamps div {
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 5px;
    padding: 10px;
    font-size: 13px;
}

.character-update-popup .update-timestamps strong {
    display: block;
    margin-bottom: 3px;
    color: var(--SmartThemeBodyColor);
    font-weight: 500;
}

.character-update-popup .popup-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.character-update-popup .popup-actions button {
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 5px;
    padding: 8px 15px;
    color: var(--SmartThemeBodyColor);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    text-decoration: none;
}

.character-update-popup .popup-actions button:hover {
    background: var(--SmartThemeBorderColor);
    border-color: var(--SmartThemeBodyColor);
}

.character-update-popup .popup-actions button i {
    font-size: 12px;
}

.character-update-popup .no-link-message {
    padding: 10px;
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 5px;
    margin-bottom: 15px;
    text-align: center;
    color: var(--SmartThemeBodyColor);
    opacity: 0.7;
    font-style: italic;
}

/* ==================== 响应式样式 ==================== */
/* 宽屏模式 - 左侧面板 */
@media screen and (min-width: 1001px) {
    .memory-modal.behavior-modal,
    .memory-modal.main-menu-modal {
        justify-content: flex-start;
        align-items: stretch;
        background: transparent;
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
        pointer-events: none;
    }

    .memory-modal.behavior-modal .memory-modal-content,
    .memory-modal.main-menu-modal .memory-modal-content {
        width: calc((100vw - var(--sheldWidth) - 2px) / 2);
        max-height: calc(100vh - var(--topBarBlockSize));
        height: calc(100vh - var(--topBarBlockSize));
        position: fixed;
        top: var(--topBarBlockSize);
        left: 0;
        margin: 0;
        border-radius: 0 10px 10px 0;
        border-left: none;
        animation: slideInLeft 0.3s ease;
        pointer-events: all;
    }

    .memory-modal.behavior-modal .memory-modal-header,
    .memory-modal.main-menu-modal .memory-modal-header {
        border-radius: 0 10px 0 0;
    }

    .memory-modal.behavior-modal .memory-modal-footer,
    .memory-modal.main-menu-modal .memory-modal-footer {
        border-radius: 0 0 10px 0;
    }

    .memory-modal.behavior-modal .memory-tab-content,
    .memory-modal.main-menu-modal .memory-tab-content {
        max-height: calc(100vh - var(--topBarBlockSize) - 120px);
    }

    .debug-log-content {
        width: 85% !important;
        max-width: 900px !important;
        max-height: 85vh !important;
    }

    /* 角色卡更新宽屏适配 */
    .character-menu-overlay {
        padding: 2rem;
    }

    .character-menu-content {
        max-width: 600px;
    }

    .menu-footer {
        justify-content: flex-end;
    }
}

/* 平板端 */
@media screen and (max-width: 1000px) and (min-width: 481px) {
    .behavior-stages-selector {
        grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
        gap: 4px;
    }

    .behavior-stage-tab {
        padding: 6px 8px;
        font-size: calc(var(--mainFontSize) * 0.8);
    }

    .memory-modal.behavior-modal .memory-modal-content,
    .memory-modal.main-menu-modal .memory-modal-content {
        width: 100vw !important;
        height: calc(100vh - var(--topBarBlockSize));
        max-width: 100vw !important;
        max-height: calc(100vh - var(--topBarBlockSize));
        position: fixed;
        top: var(--topBarBlockSize);
        left: 0;
        margin: 0;
        border-radius: 0 0 20px 20px;
        border-top: none;
        animation: slideInTop 0.3s ease;
    }

    .memory-modal.behavior-modal .memory-modal-header,
    .memory-modal.main-menu-modal .memory-modal-header {
        border-radius: 0;
        position: relative;
    }

    .memory-modal.behavior-modal .memory-modal-close,
    .memory-modal.main-menu-modal .memory-modal-close {
        position: absolute;
        top: 8px;
        right: 8px;
        font-size: 1.4em;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: var(--black30a);
    }

    .memory-modal.behavior-modal .memory-tab-content,
    .memory-modal.main-menu-modal .memory-tab-content {
        max-height: calc(100vh - var(--topBarBlockSize) - 140px);
        padding: 6px 10px;
    }

    .memory-modal.behavior-modal .memory-modal-footer,
    .memory-modal.main-menu-modal .memory-modal-footer {
        border-radius: 0;
        flex-direction: row;
        justify-content: center;
        padding: 8px 4px;
    }

    .main-menu-footer-buttons {
        width: 100%;
        justify-content: space-evenly;
        flex-direction: row;
        flex-wrap: nowrap;
    }

    .main-menu-footer-buttons .memory-action-button {
        width: auto;
        min-width: 0;
        max-width: none;
        padding: 6px 8px;
        margin: 0 2px;
        white-space: nowrap;
    }

    .tracked-name-item {
        flex-wrap: nowrap !important;
        padding: 8px 10px;
        width: 100%;
        position: relative !important;
    }

    .tracked-name-stats {
        width: auto !important;
        border-left: 1px solid rgba(255, 255, 255, 0.2) !important;
        padding-left: 8px !important;
        margin-right: 50px !important;
    }

    .tracked-name-actions {
        position: absolute !important;
        top: 50% !important;
        right: 8px !important;
        transform: translateY(-50%) !important;
    }

    .add-name-container {
        flex-direction: column;
    }

    .tracked-intimacy-input {
        width: 100%;
    }

    .debug-log-content {
        width: 95% !important;
        height: calc(100vh - var(--topBarBlockSize) - 20px) !important;
        position: fixed !important;
        top: calc(var(--topBarBlockSize) + 10px) !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    .debug-log-text {
        padding: 12px !important;
        font-size: calc(var(--mainFontSize) * 0.8) !important;
    }

    .immersive-navigation {
        gap: 8px;
        height: 30px;
    }

    .immersive-nav-btn {
        min-width: 20px;
        font-size: 10px;
    }

    /* 角色卡更新平板适配 */
    .character-menu-overlay {
        align-items: flex-start;
        padding: 2rem 1rem;
    }

    .character-menu-content {
        margin-top: 2rem;
        max-height: calc(100vh - 4rem);
        width: 100%;
        max-width: none;
    }

    .menu-header h3 {
        font-size: 16px;
    }

    .menu-body {
        padding: 15px;
    }

    .menu-footer {
        flex-wrap: wrap;
        justify-content: center;
    }

    .menu-footer button {
        flex: 1;
        min-width: 100px;
        margin: 5px;
    }
}

/* 手机端 */
@media screen and (max-width: 480px) {
    .behavior-stages-selector {
        grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
        gap: 4px;
    }

    .behavior-stage-tab {
        padding: 6px 8px;
        font-size: calc(var(--mainFontSize) * 0.8);
    }

    .memory-modal.behavior-modal .memory-modal-content,
    .memory-modal.main-menu-modal .memory-modal-content {
        width: 100vw !important;
        height: calc(100vh - var(--topBarBlockSize));
        max-width: 100vw !important;
        max-height: calc(100vh - var(--topBarBlockSize));
        position: fixed;
        top: var(--topBarBlockSize);
        left: 0;
        margin: 0;
        border-radius: 0 0 20px 20px;
        border-top: none;
        animation: slideInTop 0.3s ease;
    }

    .memory-modal.behavior-modal .memory-modal-header,
    .memory-modal.main-menu-modal .memory-modal-header {
        border-radius: 0;
        position: relative;
    }

    .memory-modal.behavior-modal .memory-modal-close,
    .memory-modal.main-menu-modal .memory-modal-close {
        position: absolute;
        top: 8px;
        right: 8px;
        font-size: 1.4em;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: var(--black30a);
    }

    .memory-modal.behavior-modal .memory-tab-content,
    .memory-modal.main-menu-modal .memory-tab-content {
        max-height: calc(100vh - var(--topBarBlockSize) - 140px);
        padding: 6px 10px;
    }

    .memory-modal.behavior-modal .memory-modal-footer,
    .memory-modal.main-menu-modal .memory-modal-footer {
        border-radius: 0;
        flex-direction: row;
        justify-content: center;
        padding: 8px 4px;
    }

    .main-menu-footer-buttons {
        width: 100%;
        justify-content: space-evenly;
        flex-direction: row;
        flex-wrap: nowrap;
    }

    .main-menu-footer-buttons .memory-action-button {
        width: auto;
        min-width: 0;
        max-width: none;
        padding: 6px 8px;
        margin: 0 2px;
        white-space: nowrap;
    }

    .tracked-name-item {
        flex-wrap: nowrap !important;
        padding: 8px 10px;
        width: 100%;
        position: relative !important;
    }

    .tracked-name-stats {
        width: auto !important;
        border-left: 1px solid rgba(255, 255, 255, 0.2) !important;
        padding-left: 8px !important;
        margin-right: 50px !important;
    }

    .tracked-name-actions {
        position: absolute !important;
        top: 50% !important;
        right: 8px !important;
        transform: translateY(-50%) !important;
    }

    .add-name-container {
        flex-direction: column;
    }

    .tracked-intimacy-input {
        width: 100%;
    }

    .user-gender-setting {
        flex-direction: column;
        align-items: flex-start;
    }

    .user-gender-setting label {
        margin-bottom: 6px;
    }

    .user-gender-select {
        width: 100%;
    }

    .add-alias-container {
        flex-direction: column;
    }

    .alias-input,
    .add-alias-button {
        width: 100%;
    }

    .tracked-gender-select {
        width: 100%;
        margin: 5px 0;
    }

    .debug-log-content {
        width: 100vw !important;
        height: calc(100vh - var(--topBarBlockSize)) !important;
        position: fixed !important;
        top: var(--topBarBlockSize) !important;
        left: 0 !important;
        border-radius: 0 0 20px 20px !important;
        border-top: none !important;
        animation: slideInTop 0.3s ease !important;
    }

    .debug-log-close {
        position: absolute !important;
        top: 8px !important;
        right: 8px !important;
        width: 32px !important;
        height: 32px !important;
        border-radius: 50% !important;
        background: var(--black30a) !important;
    }

    .debug-log-text {
        padding: 10px !important;
        font-size: calc(var(--mainFontSize) * 0.75) !important;
    }

    .debug-log-footer {
        justify-content: space-between !important;
        padding: 10px 16px !important;
    }

    .debug-log-button {
        flex: 1 !important;
        padding: 10px 12px !important;
    }

    .immersive-navigation {
        gap: 8px;
        height: 30px;
    }

    .immersive-nav-btn {
        min-width: 20px;
        font-size: 10px;
    }

    /* 角色卡更新手机适配 */
    .character-menu-overlay {
        align-items: flex-start;
        padding: 0;
    }

    .character-menu-content {
        margin-top: 0;
        max-height: 100vh;
        width: 100%;
        max-width: none;
        border-radius: 0;
    }

    .menu-header h3 {
        font-size: 16px;
    }

    .menu-body {
        padding: 15px;
    }

    .menu-footer {
        flex-wrap: wrap;
        justify-content: center;
        padding: 10px;
    }

    .menu-footer button {
        flex: 1 1 45%;
        margin: 5px;
        min-width: auto;
    }
}

/* ==================== 滚动条样式 ==================== */
.memory-tab-content::-webkit-scrollbar,
.debug-log-text::-webkit-scrollbar,
.message-preview-content-box::-webkit-scrollbar,
.xiaobai_template_editor::-webkit-scrollbar {
    width: 6px;
}

.memory-tab-content::-webkit-scrollbar-track,
.debug-log-text::-webkit-scrollbar-track,
.message-preview-content-box::-webkit-scrollbar-track,
.xiaobai_template_editor::-webkit-scrollbar-track {
    background: var(--SmartThemeBlurTintColor);
    border-radius: 3px;
}

.memory-tab-content::-webkit-scrollbar-thumb,
.debug-log-text::-webkit-scrollbar-thumb,
.message-preview-content-box::-webkit-scrollbar-thumb,
.xiaobai_template_editor::-webkit-scrollbar-thumb {
    background: var(--SmartThemeBorderColor);
    border-radius: 3px;
}

.memory-tab-content::-webkit-scrollbar-thumb:hover,
.debug-log-text::-webkit-scrollbar-thumb:hover,
.message-preview-content-box::-webkit-scrollbar-thumb:hover,
.xiaobai_template_editor::-webkit-scrollbar-thumb:hover {
    background: var(--SmartThemeAccent);
}
