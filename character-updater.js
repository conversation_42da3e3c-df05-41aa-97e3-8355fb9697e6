import { extension_settings, getContext, writeExtensionField } from "../../../extensions.js";
import { saveSettingsDebounced, eventSource, event_types, characters, this_chid } from "../../../../script.js";
import { callGenericPopup, POPUP_TYPE, POPUP_RESULT } from "../../../popup.js";

const EXT_ID = "LittleWhiteBox";
const MODULE_NAME = "characterUpdater";
const extensionFolderPath = `scripts/extensions/third-party/${EXT_ID}`;

const SECURITY_CONFIG = {
    AUTH_TOKEN: 'L15bEs6Nut9b4skgabYC',
    AUTH_HEADER_KEY: 'GTpzLYc21yopWLKhjjEQ',
    PASSWORD_SALT: 'kXUAjsi8wMa1AM8NJ9uA'
};

const moduleState = {
    isInitialized: false,
    eventHandlers: {},
    cooldownTimer: null,
    longPressTimer: null,
    cache: new Map(),
};

async function initCharacterUpdater() {
    if (moduleState.isInitialized) return;

    if (window.registerModuleCleanup) {
        window.registerModuleCleanup(MODULE_NAME, cleanup);
    }

    await addMenusHTML();

    bindMenuEvents();
    bindLongPressEvent();
    
    setupEventListeners();

    characterEditUI.addCharacterEditButton();

    if (window.addGlobalTimer) {
        const timer = setTimeout(() => startupManager.performStartupCheck(), 3000);
        window.addGlobalTimer(timer);
    }
    
    uiManager.updateDisplay();
    moduleState.isInitialized = true;
    console.log('[小白X] 角色卡更新模块已初始化');
}

function cleanup() {
    Object.keys(moduleState.eventHandlers).forEach(eventType => {
        eventSource.off(eventType, moduleState.eventHandlers[eventType]);
    });
    moduleState.eventHandlers = {};

    if (moduleState.cooldownTimer) clearInterval(moduleState.cooldownTimer);
    if (moduleState.longPressTimer) clearTimeout(moduleState.longPressTimer);
    
    $('.character-menu-overlay').remove();
    $('#character-updater-edit-button').remove();
    $('.character-update-notification').remove();

    moduleState.cache.clear();

    moduleState.isInitialized = false;
    console.log('[小白X] 角色卡更新模块已清理');
}

async function addMenusHTML() {
    try {
        const response = await fetch(`${extensionFolderPath}/character-updater-menus.html`);
        if (response.ok) {
            const menusHtml = await response.text();
            $('body').append(menusHtml);
        } else {
            throw new Error(`Failed to load menus HTML: ${response.statusText}`);
        }
    } catch (error) {
        console.error('[小白X-角色更新] 加载菜单HTML失败:', error);
    }
}

function bindMenuEvents() {
    $(document.body).on('click', '#bind-confirm', () => menuManager.handleConfirm('bind'));
    $(document.body).on('click', '#rebind-confirm', () => menuManager.handleConfirm('rebind'));
    $(document.body).on('click', '#update-confirm', () => menuManager.handleConfirm('update'));
    $(document.body).on('click', '#update-silent', () => menuManager.handleConfirm('update', true));

    ['bind', 'rebind', 'update'].forEach(type => {
        $(document.body).on('click', `#${type}-menu-close, #${type}-cancel`, () => menuManager.closeMenu(type));
    });
    
    $(document.body).on('click', '.character-menu-overlay', function(e) {
        if (e.target === this) {
            $(this).hide();
        }
    });
}

function bindLongPressEvent() {
    const trigger = $('#current-character-info-trigger');
    if (trigger.length) {
        longPressManager.start(
            trigger,
            () => uiManager.handleLongPress(),
            () => uiManager.handleShortPress()
        );
    }
}

const utils = {
    getSettings: () => {
        const parentSettings = extension_settings[EXT_ID] || {};
        const moduleSettings = parentSettings.characterUpdater || {};
        return Object.assign({}, defaultSettings, moduleSettings);
    },

    saveSettings: (newSettings) => {
        if (!extension_settings[EXT_ID]) extension_settings[EXT_ID] = {};
        extension_settings[EXT_ID].characterUpdater = Object.assign(
            extension_settings[EXT_ID].characterUpdater || {},
            newSettings
        );
        saveSettingsDebounced();
    },

    generateUUID: () => 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
        const r = Math.random() * 16 | 0;
        return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);
    }),

    showToast: (message, type = 'info') => {
        const settings = utils.getSettings();
        if (settings.showNotifications) {
            toastr[type](message, '角色卡更新');
        }
    }
};

const securityUtils = {
    encryptPassword: (password) => {
        const combined = password + SECURITY_CONFIG.PASSWORD_SALT;
        let hash = 0;
        for (let i = 0; i < combined.length; i++) {
            const char = combined.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(16);
    },

    createSecurityHeaders: () => {
        return {
            [SECURITY_CONFIG.AUTH_HEADER_KEY]: SECURITY_CONFIG.AUTH_TOKEN,
            'X-Timestamp': Date.now().toString()
        };
    },

    // Trusted domains for URL validation
    TRUSTED_DOMAINS: [
        'rentry.org',
        'discord.com',
        'discordapp.net',
        'discordapp.com'
    ],

    // Validate URL against trusted domains
    validateUrl: (url) => {
        if (!url || typeof url !== 'string') return false;

        try {
            const urlObj = new URL(url);
            const hostname = urlObj.hostname.toLowerCase();

            // Check if hostname matches any trusted domain or is a subdomain of trusted domains
            return securityUtils.TRUSTED_DOMAINS.some(domain =>
                hostname === domain || hostname.endsWith('.' + domain)
            );
        } catch (error) {
            return false;
        }
    },

    // Sanitize HTML content to allow safe tags but prevent dangerous ones
    sanitizeContent: (content) => {
        if (!content || typeof content !== 'string') return '';

        // List of allowed safe HTML tags
        const allowedTags = ['br', 'b', 'strong', 'i', 'em', 'u', 'p', 'div', 'span'];

        // Create a temporary div to parse HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = content;

        // Function to recursively clean nodes
        const cleanNode = (node) => {
            if (node.nodeType === Node.TEXT_NODE) {
                return node.textContent;
            }

            if (node.nodeType === Node.ELEMENT_NODE) {
                const tagName = node.tagName.toLowerCase();

                // If tag is not allowed, return only text content
                if (!allowedTags.includes(tagName)) {
                    return node.textContent;
                }

                // For allowed tags, recreate them without any attributes (to remove onclick, href, etc.)
                let result = `<${tagName}>`;
                for (let child of node.childNodes) {
                    result += cleanNode(child);
                }
                result += `</${tagName}>`;
                return result;
            }

            return '';
        };

        // Clean all child nodes
        let sanitized = '';
        for (let child of tempDiv.childNodes) {
            sanitized += cleanNode(child);
        }

        return sanitized;
    }
};

const characterManager = {
    getCharacter: id => id != null ? getContext().characters[id] || null : null,
    
    getExtensionData: id => characterManager.getCharacter(id)?.data?.extensions?.[MODULE_NAME] || null,
    
    saveExtensionData: async (id, data) => {
        try {
            await writeExtensionField(id, MODULE_NAME, data);
            return true;
        } catch (error) {
            console.error(`保存失败:`, error);
            return false;
        }
    },
    
    isBound: id => {
        const data = characterManager.getExtensionData(id);
        return !!(data?.uniqueValue && data?.nameGroup);
    },
    
    getAllBound: () => characters.reduce((acc, char, index) => {
        if (char && characterManager.isBound(index)) acc.push(index);
        return acc;
    }, [])
};

const serverAPI = {
    cache: new Map(),
    cacheTimeout: 5 * 60 * 1000,

    clearCache: () => serverAPI.cache.clear(),

    request: async (endpoint, method = 'GET', data = null) => {
        const { serverUrl } = utils.getSettings();
        if (!serverUrl) throw new Error('服务器地址未配置');

        const baseUrl = serverUrl.endsWith('/') ? serverUrl.slice(0, -1) : serverUrl;
        const fullUrl = `${baseUrl}${endpoint}`;
        const cacheKey = fullUrl;

        if (method === 'GET') {
            const cached = serverAPI.cache.get(cacheKey);
            if (cached && Date.now() - cached.timestamp < serverAPI.cacheTimeout) {
                return cached.data;
            }
        }

        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                ...securityUtils.createSecurityHeaders()
            },
            ...(data && { body: JSON.stringify(data) })
        };

        console.log(`[Character Updater] ${method} ${fullUrl}`, data ? data : '');
        const response = await fetch(fullUrl, options);
        if (!response.ok) {
            const error = await response.json().catch(() => ({}));
            const errorMessage = error.error || `服务器错误: ${response.status}`;

            const errorObj = new Error(errorMessage);
            errorObj.status = response.status;
            errorObj.isPasswordError = response.status === 401 && (errorMessage.includes('密码') || errorMessage.includes('password'));
            throw errorObj;
        }

        const result = await response.json();

        if (method === 'GET') {
            serverAPI.cache.set(cacheKey, { data: result, timestamp: Date.now() });
        }

        return result;
    },

    create: data => serverAPI.request('/create', 'POST', data),
    update: data => serverAPI.request('/update', 'PUT', data),

    batchData: async (characters) => {
        const payload = {
            characters: characters.map((char, index) => ({
                name: char.nameGroup || char.name,
                uniqueValue: char.uniqueValue,
                clientId: index
            }))
        };
        return serverAPI.request('/batch/data', 'POST', payload);
    },


};

const cooldownManager = {
    isActive: false,
    timeLeft: 0,
    timer: null,

    start: (duration = 30) => {
        cooldownManager.isActive = true;
        cooldownManager.timeLeft = duration;

        cooldownManager.timer = setInterval(() => {
            cooldownManager.timeLeft--;
            if (cooldownManager.timeLeft <= 0) cooldownManager.stop();
        }, 1000);
    },

    stop: () => {
        cooldownManager.isActive = false;
        cooldownManager.timeLeft = 0;
        clearInterval(cooldownManager.timer);
        cooldownManager.timer = null;
    },

    check: () => {
        if (cooldownManager.isActive) {
            utils.showToast(`操作冷却中，请等待 ${cooldownManager.timeLeft} 秒`, 'warning');
            return false;
        }
        return true;
    }
};

const dataCache = {
    cache: new Map(),

    set: (key, data) => dataCache.cache.set(key, { ...data, cachedAt: Date.now() }),

    get: key => {
        const cached = dataCache.cache.get(key);
        if (!cached) return null;

        const isExpired = Date.now() - cached.cachedAt > (cached.cacheTimeout || 10 * 60 * 1000);
        if (isExpired) {
            dataCache.cache.delete(key);
            return null;
        }
        return cached;
    },

    getCloudData: charId => dataCache.get(charId)?.serverData || null,

    setBatch: (dataMap) => {
        const timestamp = Date.now();
        dataMap.forEach((data, key) => {
            dataCache.cache.set(key, { ...data, cachedAt: timestamp });
        });
    },


};

const longPressManager = {
    isPressed: false,
    timer: null,
    duration: 3000,

    start: (element, onLongPress, onShortPress) => {
        let startTime;

        element.on('mousedown touchstart', () => {
            longPressManager.isPressed = true;
            startTime = Date.now();
            
            longPressManager.timer = setTimeout(() => {
                if (longPressManager.isPressed) {
                    longPressManager.isPressed = false;
                    onLongPress();
                }
            }, longPressManager.duration);
        });

        element.on('mouseup mouseleave touchend touchcancel', () => {
            if (longPressManager.timer) {
                clearTimeout(longPressManager.timer);
                longPressManager.timer = null;
            }

            if (longPressManager.isPressed) {
                const pressDuration = Date.now() - startTime;
                longPressManager.isPressed = false;
                
                if (pressDuration < longPressManager.duration) {
                    onShortPress();
                }
            }
        });
    }
};

const menuManager = {
    showMenu: type => {
        $('.character-menu-overlay').hide();
        menuManager.updateUUIDDisplay(type);
        $(`#${type}-character-menu`).show();
    },

    closeMenu: type => {
        $(`#${type}-character-menu`).hide();
        menuManager.clearForm(type);
    },

    updateUUIDDisplay: type => {
        if (this_chid == null) return;
        const data = characterManager.getExtensionData(this_chid);

        const displays = {
            'bind': () => $('#bind-uuid-display').text('将自动生成'),
            'rebind': () => {
                $('#rebind-current-uuid').text(data?.uniqueValue || '未绑定');
                $('#rebind-new-uuid').text('将自动生成');
            },
            'update': () => $('#update-uuid-display').text(data?.uniqueValue || '未绑定')
        };
        
        displays[type]?.();
    },

    clearForm: type => {
        $(`#${type}-password, #${type}-update-note, #${type}-png-url`).val('');
    },

    getFormData: type => ({
        password: $(`#${type}-password`).val().trim(),
        updateNote: $(`#${type}-update-note`).val().trim() || 
            (type === 'bind' ? '初始版本' : type === 'rebind' ? '重新绑定' : '版本更新'),
        pngUrl: $(`#${type}-png-url`).val().trim()
    }),

    validateForm: (type, data) => {
        if (!data.password) {
            utils.showToast('请输入密码', 'error');
            return false;
        }
        if (data.password.length < 4) {
            utils.showToast('密码至少需要4个字符', 'error');
            return false;
        }

        // Validate URL against trusted domains
        if (data.pngUrl && !securityUtils.validateUrl(data.pngUrl)) {
            utils.showToast('链接地址只能使用受信任的域名 (rentry.org, discord.com, discordapp.net, discordapp.com)', 'error');
            return false;
        }

        return true;
    },

    checkContentLimits: (data) => {
        const errors = [];
        if (data.updateNote && data.updateNote.length > 300) {
            errors.push('更新公告超过300字限制');
        }
        if (data.pngUrl && data.pngUrl.length > 300) {
            errors.push('链接地址超过300字限制');
        }
        return errors;
    },

    handleConfirm: async (type, isSilent = false) => {
        if (!cooldownManager.check() || this_chid == null) {
            if (this_chid == null) utils.showToast('请先选择一个角色', 'error');
            return;
        }

        const formData = menuManager.getFormData(type);
        if (!menuManager.validateForm(type, formData)) return;

        const contentErrors = menuManager.checkContentLimits(formData);
        if (contentErrors.length > 0) {
            utils.showToast(contentErrors.join('，'), 'error');
            return;
        }

        const buttonSelector = isSilent ? `#${type}-silent` : `#${type}-confirm`;
        const $button = $(buttonSelector);
        const originalText = $button.text();

        try {
            $button.prop('disabled', true).text(isSilent ? '静默更新中...' : '处理中...');

            const actions = {
                'bind': () => businessLogic.bindCharacter(this_chid, formData.password, formData.updateNote, formData.pngUrl),
                'rebind': async () => {
                    await characterManager.saveExtensionData(this_chid, {});
                    return businessLogic.bindCharacter(this_chid, formData.password, formData.updateNote, formData.pngUrl);
                },
                'update': () => isSilent ?
                    businessLogic.silentUpdateCharacter(this_chid, formData.password, formData.updateNote, formData.pngUrl) :
                    businessLogic.updateCharacter(this_chid, formData.password, formData.updateNote, formData.pngUrl)
            };

            const result = await actions[type]();

            if (result.success) {
                const actionText = type === 'bind' ? '绑定' : type === 'rebind' ? '重新绑定' : (isSilent ? '静默更新' : '更新');
                utils.showToast(`角色${actionText}成功！`, 'success');


                cooldownManager.start(30);
                menuManager.closeMenu(type);

                setTimeout(() => {
                    uiManager.updateDisplay();
                }, 500);
            } else {
                if (result.error && result.error.includes('密码错误')) {
                    utils.showToast('密码错误，请检查密码', 'error');
                } else if (result.error && (result.error.includes('格式无效') || result.error.includes('长度'))) {
                    utils.showToast(`数据格式错误: ${result.error}`, 'error');
                } else {
                    utils.showToast(`操作失败: ${result.error}`, 'error');
                }
            }
        } catch (error) {
            console.error(`${type}操作失败:`, error);

            if (error.isPasswordError || (error.message && error.message.includes('密码错误'))) {
                utils.showToast('密码错误，请检查密码', 'error');
            } else if (error.message && (error.message.includes('格式无效') || error.message.includes('长度'))) {
                utils.showToast(`数据格式错误: ${error.message}`, 'error');
            } else {
                utils.showToast('操作失败，请检查网络连接', 'error');
            }
        } finally {
            $button.prop('disabled', false).text(originalText);
        }
    }
};

const uiManager = {
    init: () => {
        uiManager.updateDisplay();
        uiManager.bindEvents();
    },

    bindEvents: () => {
        const trigger = $('#current-character-info-trigger');
        
        longPressManager.start(
            trigger,
            () => uiManager.handleLongPress(),
            () => uiManager.handleShortPress()
        );

        ['bind', 'rebind', 'update'].forEach(type => {
            $(`#${type}-menu-close, #${type}-cancel`).on('click', () => menuManager.closeMenu(type));
            $(`#${type}-confirm`).on('click', () => menuManager.handleConfirm(type));
        });

        $('#update-silent').on('click', () => menuManager.handleConfirm('update', true));

        $('.character-menu-overlay').on('click', function(e) {
            if (e.target === this) {
                const menuType = $(this).attr('id').replace('-character-menu', '');
                menuManager.closeMenu(menuType);
            }
        });
    },

    handleLongPress: () => {
        if (this_chid == null) {
            utils.showToast('请先选择一个角色', 'warning');
            return;
        }
        menuManager.showMenu(characterManager.isBound(this_chid) ? 'rebind' : 'bind');
    },

    handleShortPress: () => {
        if (this_chid == null) {
            utils.showToast('请先选择一个角色', 'warning');
            return;
        }
        
        if (characterManager.isBound(this_chid)) {
            menuManager.showMenu('update');
        } else {
            utils.showToast('角色尚未绑定，请长按5秒进行绑定', 'info');
        }
    },

    updateDisplay: () => {
        const $name = $('#current-character-name');
        const $status = $('#current-character-status');

        if (this_chid == null) {
            $name.text('未选择角色');
            $status.removeClass().text('');
            characterEditUI.updateButtonState(false);
            return;
        }

        const character = characterManager.getCharacter(this_chid);
        if (!character) return;

        $name.text(character.name);

        const isBound = characterManager.isBound(this_chid);
        $status.removeClass().addClass(isBound ? 'bound' : 'unbound')
            .text(isBound ? '已绑定' : '未绑定');

        if (!isBound) {
            characterEditUI.updateButtonState(false);
            characterListUI.removeUpdateNotification(this_chid);
        }
    },

    checkCurrentCharacterUpdate: async () => {
        if (this_chid == null || !characterManager.isBound(this_chid)) {
            characterEditUI.updateButtonState(false);
            return;
        }

        try {
            const data = characterManager.getExtensionData(this_chid);
            if (!data?.uniqueValue || !data?.nameGroup) return;

            let cloudData = dataCache.getCloudData(this_chid);

            if (!cloudData) {
                const result = await serverAPI.batchData([{
                    nameGroup: data.nameGroup,
                    uniqueValue: data.uniqueValue,
                    clientId: 0,
                    localTimestamp: data.timestamp
                }]);

                if (result.success && result.results && result.results[0]?.found) {
                    cloudData = result.results[0].data;
                    dataCache.set(this_chid, {
                        serverData: cloudData,
                        cacheTimeout: 10 * 60 * 1000
                    });
                }
            }

            const hasUpdate = cloudData?.timestamp && cloudData.timestamp !== data.timestamp;
            characterEditUI.updateButtonState(hasUpdate);

            if (hasUpdate) {
                const character = characterManager.getCharacter(this_chid);
                const updateInfo = {
                    characterId: this_chid,
                    characterName: character?.name || '未知角色',
                    currentTimestamp: data.timestamp,
                    latestTimestamp: cloudData.timestamp,
                    updateNote: cloudData.update_notice || '无更新说明',
                    linkAddress: cloudData.link_address || '',
                    serverData: cloudData
                };
                characterListUI.addUpdateNotification(this_chid, updateInfo);
            } else {
                characterListUI.removeUpdateNotification(this_chid);
            }

        } catch (error) {
            console.error('检查角色更新状态失败:', error);
            characterEditUI.updateButtonState(false);
        }
    },

    restoreUpdateNotifications: async () => {
        try {
            const boundCharacters = characterManager.getAllBound();
            if (boundCharacters.length === 0) return;

            for (const charId of boundCharacters) {
                const data = characterManager.getExtensionData(charId);
                const cloudData = dataCache.getCloudData(charId);

                if (data && cloudData && cloudData.timestamp && cloudData.timestamp !== data.timestamp) {
                    const character = characterManager.getCharacter(charId);
                    const updateInfo = {
                        characterId: charId,
                        characterName: character?.name || '未知角色',
                        currentTimestamp: data.timestamp,
                        latestTimestamp: cloudData.timestamp,
                        updateNote: cloudData.update_notice || '无更新说明',
                        linkAddress: cloudData.link_address || '',
                        serverData: cloudData
                    };
                    characterListUI.addUpdateNotification(charId, updateInfo);
                }
            }
        } catch (error) {
            console.error('恢复更新通知失败:', error);
        }
    }
};

const businessLogic = {
    bindCharacter: async (id, password, updateNote, pngUrl) => {
        const character = characterManager.getCharacter(id);
        if (!character) return { success: false, error: '角色不存在' };

        const uuid = utils.generateUUID();
        const timestamp = new Date().toISOString();
        let nameGroup = character.name || "无名称角色卡";

        if (nameGroup.length > 300) {
            nameGroup = nameGroup.substring(0, 300);
        }

        try {
            const result = await serverAPI.create({
                name: nameGroup,
                unique_value: uuid,
                password: securityUtils.encryptPassword(password),
                update_notice: updateNote,
                link_address: pngUrl,
                timestamp
            });

            if (!result.success) return { success: false, error: result.error };

            await characterManager.saveExtensionData(id, {
                nameGroup,
                uniqueValue: uuid,
                updateNote,
                linkAddress: pngUrl,
                timestamp,
                bindTime: Date.now()
            });

            return { success: true, nameGroup, uniqueValue: uuid };
        } catch (error) {
            console.error('绑定失败:', error);
            if (error.isPasswordError || (error.message && error.message.includes('密码错误'))) {
                return { success: false, error: '密码错误' };
            }
            return { success: false, error: '网络连接失败' };
        }
    },

    updateCharacter: async (id, password, updateNote, pngUrl) => {
        const data = characterManager.getExtensionData(id);
        if (!characterManager.isBound(id)) return { success: false, error: '角色未绑定' };

        const timestamp = new Date().toISOString();

        try {
            const result = await serverAPI.update({
                name: data.nameGroup,
                unique_value: data.uniqueValue,
                password: securityUtils.encryptPassword(password),
                update_notice: updateNote,
                link_address: pngUrl,
                timestamp
            });

            if (!result.success) return { success: false, error: result.error };

            await characterManager.saveExtensionData(id, {
                ...data,
                updateNote,
                linkAddress: pngUrl,
                timestamp,
                lastUpdateTime: Date.now()
            });

            dataCache.set(id, {
                serverData: { timestamp, update_notice: updateNote, link_address: pngUrl },
                cacheTimeout: 10 * 60 * 1000
            });

            return { success: true, timestamp };
        } catch (error) {
            console.error('更新失败:', error);
            if (error.isPasswordError || (error.message && error.message.includes('密码错误'))) {
                return { success: false, error: '密码错误' };
            }
            return { success: false, error: '网络连接失败' };
        }
    },

    silentUpdateCharacter: async (id, password, updateNote, pngUrl) => {
        const data = characterManager.getExtensionData(id);
        if (!characterManager.isBound(id)) return { success: false, error: '角色未绑定' };

        try {
            const result = await serverAPI.update({
                name: data.nameGroup,
                unique_value: data.uniqueValue,
                password: securityUtils.encryptPassword(password),
                update_notice: updateNote,
                link_address: pngUrl
            });

            if (!result.success) return { success: false, error: result.error };

            await characterManager.saveExtensionData(id, {
                ...data,
                updateNote,
                linkAddress: pngUrl,
                lastSilentUpdateTime: Date.now()
            });

            dataCache.set(id, {
                serverData: {
                    timestamp: data.timestamp,
                    update_notice: updateNote,
                    link_address: pngUrl
                },
                cacheTimeout: 10 * 60 * 1000
            });

            return { success: true, timestamp: data.timestamp };
        } catch (error) {
            console.error('静默更新失败:', error);
            if (error.isPasswordError || (error.message && error.message.includes('密码错误'))) {
                return { success: false, error: '密码错误' };
            }
            return { success: false, error: '网络连接失败' };
        }
    },


};

const characterListUI = {
    addUpdateNotification: (characterId, updateInfo) => {
        const characterElement = $(`#CharID${characterId}`);
        if (!characterElement.length) return;

        const nameBlock = characterElement.find('.character_name_block');
        if (!nameBlock.length) return;

        nameBlock.find('.character-update-notification').remove();

        const updateNotification = $(`
            <span class="character-update-notification" data-character-id="${characterId}">
                <i class="fa-solid fa-circle-exclamation"></i>
                <small>有可用更新</small>
            </span>
        `);

        updateNotification.on('click', e => {
            e.stopPropagation();
            popupManager.showUpdatePopup(updateInfo);
        });

        nameBlock.append(updateNotification);
    },

    removeUpdateNotification: characterId => {
        $(`#CharID${characterId}`).find('.character-update-notification').remove();
    },


};

const popupManager = {
    formatSimpleDate: timestamp => {
        if (!timestamp) return 'Unknown';
        return new Date(timestamp).toLocaleDateString();
    },

    showUpdatePopup: async updateInfo => {
        const hasUpdate = updateInfo?.latestTimestamp &&
                         updateInfo.latestTimestamp !== updateInfo.currentTimestamp;

        // Sanitize announcement content to prevent HTML injection and make it non-interactive
        const rawAnnouncementText = hasUpdate ?
            updateInfo.updateNote : (updateInfo?.updateNote || 'No announcements available');
        const sanitizedAnnouncementText = securityUtils.sanitizeContent(rawAnnouncementText);

        const linkAddress = updateInfo?.linkAddress || '';

        // Validate link address against trusted domains
        const isValidUrl = linkAddress ? securityUtils.validateUrl(linkAddress) : false;
        const sanitizedLinkAddress = isValidUrl ? securityUtils.sanitizeContent(linkAddress) : '';

        const popupContent = `
            <div class="character-update-popup">
                <h3>${securityUtils.sanitizeContent(updateInfo?.characterName || 'Unknown')} 更新信息</h3>
                <div class="update-status">
                    <div class="status-message ${hasUpdate ? 'status-update' : 'status-success'}">
                        <i class="fa-solid ${hasUpdate ? 'fa-exclamation-circle' : 'fa-check-circle'}"></i>
                        ${hasUpdate ? '有可用更新' : '你的角色卡已是最新版本'}
                    </div>
                </div>
                <div class="update-description">
                    <strong>${hasUpdate ? '最新更新公告:' : '上次更新公告:'}</strong>
                    <div class="announcement-content" style="user-select: none; pointer-events: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none;">${sanitizedAnnouncementText}</div>
                </div>
                <div class="update-description">
                    <strong>${hasUpdate ? '最新更新地址:' : '更新地址:'}</strong>
                    <div class="link-content" style="user-select: none; pointer-events: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none;">${sanitizedLinkAddress || (linkAddress ? '该链接地址非dc或rentry来源, 不予显示' : '无链接地址')}</div>
                </div>
                <div class="update-timestamps">
                    <div>
                        <strong>上次更新时间:</strong>
                        ${popupManager.formatSimpleDate(updateInfo?.currentTimestamp)}
                    </div>
                    <div>
                        <strong>最新更新时间:</strong>
                        ${popupManager.formatSimpleDate(updateInfo?.latestTimestamp)}
                    </div>
                </div>
                ${isValidUrl ? `
                    <div class="popup-actions">
                        <button class="menu_button" onclick="window.open('${linkAddress}', '_blank', 'noopener,noreferrer')">
                            <i class="fa-solid fa-external-link-alt"></i>
                            一键打开更新地址
                        </button>
                    </div>
                ` : ''}
            </div>
        `;

        await callGenericPopup(popupContent, POPUP_TYPE.DISPLAY, '');
    },

    showGeneralInfoPopup: async characterName => {
        const characterData = characterManager.getExtensionData(this_chid);
        let cloudData = dataCache.getCloudData(this_chid);

        if (!cloudData && characterData?.uniqueValue && characterData?.nameGroup) {
            try {
                const result = await serverAPI.batchData([{
                    nameGroup: characterData.nameGroup,
                    uniqueValue: characterData.uniqueValue,
                    clientId: 0,
                    localTimestamp: characterData.timestamp
                }]);

                if (result.success && result.results && result.results[0]?.found) {
                    cloudData = result.results[0].data;
                    dataCache.set(this_chid, {
                        serverData: cloudData,
                        cacheTimeout: 10 * 60 * 1000
                    });
                }
            } catch (error) {
                console.error('获取角色云端数据失败:', error);
            }
        }

        await popupManager.showUpdatePopup({
            characterName,
            currentTimestamp: characterData?.timestamp || new Date().toISOString(),
            latestTimestamp: cloudData?.timestamp || characterData?.timestamp || new Date().toISOString(),
            updateNote: cloudData?.update_notice || characterData?.updateNote || '',
            linkAddress: cloudData?.link_address || characterData?.linkAddress || ''
        });
    }
};

const startupManager = {
    isStartupCheckComplete: false,
    startupCache: new Map(),

    performStartupCheck: async () => {
        if (startupManager.isStartupCheckComplete) {
            console.log('启动检查已完成，使用缓存数据');
            return;
        }

        try {
            const boundCharacters = characterManager.getAllBound();
            if (boundCharacters.length === 0) {
                startupManager.isStartupCheckComplete = true;
                return;
            }

            console.log(`开始批量检查 ${boundCharacters.length} 个绑定角色`);

            const charactersToCheck = [];
            const characterMap = new Map();

            boundCharacters.forEach((charId, index) => {
                const data = characterManager.getExtensionData(charId);
                if (data?.uniqueValue && data?.nameGroup) {
                    charactersToCheck.push({
                        nameGroup: data.nameGroup,
                        uniqueValue: data.uniqueValue,
                        clientId: index,
                        localTimestamp: data.timestamp
                    });
                    characterMap.set(index, charId);
                }
            });

            if (charactersToCheck.length === 0) {
                startupManager.isStartupCheckComplete = true;
                return;
            }

            const batchResult = await serverAPI.batchData(charactersToCheck);

            if (batchResult.success && batchResult.results) {
                const cacheMap = new Map();
                const updates = [];

                batchResult.results.forEach(result => {
                    if (result.found && result.data) {
                        const charId = characterMap.get(result.clientId);
                        const localData = characterManager.getExtensionData(charId);

                        cacheMap.set(charId, {
                            serverData: result.data,
                            cacheTimeout: 30 * 60 * 1000
                        });

                        if (result.data.timestamp && result.data.timestamp !== localData.timestamp) {
                            const character = characterManager.getCharacter(charId);
                            updates.push({
                                characterId: charId,
                                characterName: character?.name || '未知角色',
                                currentTimestamp: localData.timestamp,
                                latestTimestamp: result.data.timestamp,
                                updateNote: result.data.update_notice || '无更新说明',
                                linkAddress: result.data.link_address || '',
                                serverData: result.data
                            });
                        }
                    }
                });

                dataCache.setBatch(cacheMap);

                $('.character-update-notification').remove();
                updates.forEach(update => {
                    characterListUI.addUpdateNotification(update.characterId, update);
                });

                if (this_chid != null) {
                    const currentCharacterHasUpdate = updates.some(update => update.characterId === this_chid);
                    characterEditUI.updateButtonState(currentCharacterHasUpdate);

                    if (!currentCharacterHasUpdate && characterManager.isBound(this_chid)) {
                        setTimeout(() => uiManager.checkCurrentCharacterUpdate(), 1000);
                    }
                }

                console.log(`启动检查完成，发现 ${updates.length} 个角色有更新`);
            }

            startupManager.isStartupCheckComplete = true;

        } catch (error) {
            console.error('启动批量检查失败:', error);
            startupManager.isStartupCheckComplete = true;
        }
    },

    resetStartupCheck: () => {
        startupManager.isStartupCheckComplete = false;
        startupManager.startupCache.clear();
    }
};

const characterEditUI = {
    addCharacterEditButton: () => {
        if ($('#character-updater-edit-button').length > 0) return;

        const buttonHtml = `
            <div id="character-updater-edit-button" class="menu_button fa-solid fa-cloud-arrow-down interactable"
                 title="Check for character updates">
            </div>
        `;

        $('.form_create_bottom_buttons_block').prepend(buttonHtml);

        $('#character-updater-edit-button').on('click', async () => {
            if (this_chid == null) {
                utils.showToast('No character selected', 'warning');
                return;
            }

            if (!characterManager.isBound(this_chid)) {
                utils.showToast('Character is not bound to update service', 'warning');
                return;
            }

            try {
                const character = characterManager.getCharacter(this_chid);
                await popupManager.showGeneralInfoPopup(character.name);
            } catch (error) {
                console.error('显示角色信息失败:', error);
                utils.showToast('Failed to show character info', 'error');
            }
        });
    },

    updateButtonState: hasUpdate => {
        $('#character-updater-edit-button').toggleClass('has-update', hasUpdate);
    }
};

function setupEventListeners() {
    moduleState.eventHandlers.chatChanged = async () => {
        uiManager.updateDisplay();
        if (this_chid != null && characterManager.isBound(this_chid)) {
            await uiManager.checkCurrentCharacterUpdate();
        }
    };
    eventSource.on(event_types.CHAT_CHANGED, moduleState.eventHandlers.chatChanged);

    moduleState.eventHandlers.characterEdited = async () => {
        uiManager.updateDisplay();
    };
    eventSource.on(event_types.CHARACTER_EDITED, moduleState.eventHandlers.characterEdited);

    moduleState.eventHandlers.characterPageLoaded = async () => {
        await uiManager.restoreUpdateNotifications();
    };
    eventSource.on(event_types.CHARACTER_PAGE_LOADED, moduleState.eventHandlers.characterPageLoaded);
}

const defaultSettings = {
    enabled: true,
    showNotifications: true,
    serverUrl: "https://db.littlewhitebox.qzz.io",
    boundCharacters: {}
};

export { initCharacterUpdater };
